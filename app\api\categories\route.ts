import { NextResponse } from "next/server"
import { getAllCategories } from "@/lib/data"
import type { ApiResponse, Category } from "@/types"

export async function GET() {
  try {
    const categories = getAllCategories()

    const response: ApiResponse<Category[]> = {
      success: true,
      data: categories,
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch categories",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
