import { formatPrice, calculateSavings, type CurrencyCode } from "@/lib/utils/currency"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface PriceProps {
  price: number
  originalPrice?: number
  currency?: CurrencyCode
  locale?: string
  size?: "sm" | "md" | "lg"
  showSavings?: boolean
  className?: string
}

export function Price({
  price,
  originalPrice,
  currency = "USD",
  locale = "en-US",
  size = "md",
  showSavings = true,
  className,
}: PriceProps) {
  const formattedPrice = formatPrice(price, currency, locale)
  const formattedOriginalPrice = originalPrice ? formatPrice(originalPrice, currency, locale) : null
  const savings = originalPrice ? calculateSavings(originalPrice, price) : null

  const sizeClasses = {
    sm: "text-lg",
    md: "text-2xl",
    lg: "text-3xl",
  }

  return (
    <div className={cn("flex items-center gap-2 flex-wrap", className)}>
      <span className={cn("font-bold text-foreground", sizeClasses[size])}>{formattedPrice}</span>

      {formattedOriginalPrice && (
        <span
          className={cn(
            "text-muted-foreground line-through",
            size === "sm" ? "text-base" : size === "md" ? "text-xl" : "text-2xl",
          )}
        >
          {formattedOriginalPrice}
        </span>
      )}

      {showSavings && savings && savings.percentage > 0 && (
        <Badge variant="destructive">Save {savings.percentage}%</Badge>
      )}
    </div>
  )
}
