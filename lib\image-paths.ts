// Centralized image path management
export const imagePaths = {
  // Product images - organized by product ID
  products: {
    "yeye-go": {
      main: "/images/products/yeye-go/main.png",
      lifestyle: ["/images/products/yeye-go/lifestyle-1.jpg", "/images/products/yeye-go/lifestyle-2.jpg"],
      details: ["/images/technical/diagram.png"],
      pink: {
        main: "/images/products/yeye-go/main.png", // No pink variant yet
        lifestyle: ["/images/products/yeye-go/lifestyle-1.jpg", "/images/products/yeye-go/lifestyle-2.jpg"],
        details: ["/images/technical/color-variants.jpg"],
      }
    },
    "yeye-classic": {
      main: "/images/products/yeye-classic/main.jpg",
      lifestyle: ["/images/products/yeye-classic/lifestyle-1.jpg", "/images/products/yeye-classic/lifestyle-2.jpg"],
      details: ["/images/products/yeye-classic/macro-pink.png"],
      pink: {
        main: "/images/products/yeye-classic/main-pink.png",
        lifestyle: ["/images/products/yeye-classic/lifestyle-1.jpg", "/images/products/yeye-classic/lifestyle-2.jpg"],
        details: ["/images/products/yeye-classic/detail-pink.jpeg"],
      }
    },
    "yeye-weekender": {
      main: "/images/products/yeye-weekender/main.png",
      lifestyle: ["/images/products/yeye-weekender/lifestyle-1.jpg"],
      details: ["/images/products/yeye-weekender/lifestyle-pink.png"],
      pink: {
        main: "/images/products/yeye-weekender/main-pink.png",
        lifestyle: ["/images/products/yeye-weekender/lifestyle-pink.png"],
        details: ["/images/products/yeye-weekender/lifestyle-pink.png"],
      }
    },
    "yeye-jug": {
      main: "/images/products/yeye-jug/main.jpg",
      lifestyle: ["/images/products/yeye-jug/lifestyle-1.jpg", "/images/products/yeye-jug/lifestyle-2.jpg"],
      details: ["/images/products/yeye-jug/detail.jpg", "/images/products/yeye-jug/angle.jpg"],
      pink: {
        main: "/images/products/yeye-jug/main-pink.jpeg",
        lifestyle: ["/images/products/yeye-jug/lifestyle-1.jpg", "/images/products/yeye-jug/lifestyle-2.jpg"],
        details: ["/images/products/yeye-jug/detail.jpg", "/images/products/yeye-jug/angle.jpg"],
      }
    },
  },

  // Hero and lifestyle images
  hero: {
    main: "/images/hero/beach-main.jpg",
    pink: "/images/hero/beach-main-pink.png",
    secondary: "/images/lifestyle/sunset-dock.jpg",
  },

  // Lifestyle images
  lifestyle: {
    beach: "/images/hero/beach-main.jpg",
    beachPink: "/images/hero/beach-main-pink.png",
    picnic: "/images/lifestyle/group-picnic.jpg",
    pool: "/images/lifestyle/pool-party.jpg",
    sunset: "/images/lifestyle/sunset-dock.jpg",
    jug: "/images/lifestyle/jug-lifestyle.jpg",
  },

  // Family collection images
  family: {
    main: "/images/products/yeye-weekender/main.png",
    pink: "/images/products/yeye-weekender/main-pink.png",
  },

  // Category images
  categories: {
    coolers: "/images/categories/coolers.jpg",
    jugs: "/images/categories/jugs.jpg",
    accessories: "/images/technical/diagram.png",
  },

  // Testimonial images
  testimonials: {
    sarah: "/images/testimonials/sarah.jpg",
    mike: "/images/testimonials/mike.jpg",
    emma: "/images/testimonials/emma.jpg",
  },

  // Technical and brand images
  technical: {
    diagram: "/images/technical/diagram.png",
    colorVariants: "/images/technical/color-variants.jpg",
    construction: "/images/products/yeye-classic/detail-pink.jpeg",
  },

  // Brand assets
  brand: {
    logo: "/placeholder-logo.svg",
    logoMark: "/placeholder-logo.png",
    favicon: "/placeholder-logo.png",
  },
}

// Helper function to get product images
export const getProductImages = (productId: string, variant: "default" | "pink" = "default") => {
  const product = imagePaths.products[productId as keyof typeof imagePaths.products]
  if (!product) return []

  if (variant === "pink" && product.pink) {
    return [product.pink.main, ...product.pink.lifestyle, ...product.pink.details]
  }

  return [product.main, ...product.lifestyle, ...product.details]
}

// Helper function to get product main image with variant support
export const getProductMainImage = (productId: string, variant: "default" | "pink" = "default") => {
  const product = imagePaths.products[productId as keyof typeof imagePaths.products]
  if (!product) return fallbackImages.product

  if (variant === "pink" && product.pink) {
    return product.pink.main
  }

  return product.main
}

// Helper function to get hero image with variant support
export const getHeroImage = (variant: "default" | "pink" = "default") => {
  if (variant === "pink") {
    return imagePaths.hero.pink
  }
  return imagePaths.hero.main
}

// Helper function to get family image with variant support
export const getFamilyImage = (variant: "default" | "pink" = "default") => {
  if (variant === "pink") {
    return imagePaths.family.pink
  }
  return imagePaths.family.main
}

// Centralized fallback image configuration
export const fallbackImages = {
  product: "/placeholder.svg?height=400&width=400&text=Product+Image",
  hero: "/placeholder.svg?height=600&width=1200&text=Hero+Image",
  thumbnail: "/placeholder.svg?height=100&width=100&text=Thumbnail",
  avatar: "/placeholder-user.jpg",
  category: "/placeholder.svg?height=300&width=300&text=Category",
  default: "/placeholder.svg",
}

// Helper function to get image with fallback
export const getImageWithFallback = (imagePath: string, fallbackType: keyof typeof fallbackImages = "default") => {
  return imagePath || fallbackImages[fallbackType]
}
