"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Star, ThumbsUp, Filter } from "lucide-react"

interface Review {
  id: string
  author: string
  avatar: string
  rating: number
  date: string
  title: string
  content: string
  verified: boolean
  helpful: number
  images?: string[]
}

const reviews: Review[] = [
  {
    id: "1",
    author: "<PERSON>",
    avatar: "/placeholder-user.jpg",
    rating: 5,
    date: "2024-01-15",
    title: "Perfect for beach days!",
    content:
      "I love how I can see everything inside without opening it. The transparency is not just a gimmick - it's actually super practical. Keeps ice for the whole day at the beach.",
    verified: true,
    helpful: 24,
    images: ["/images/pool-party.jpg"],
  },
  {
    id: "2",
    author: "<PERSON>",
    avatar: "/placeholder-user.jpg",
    rating: 5,
    date: "2024-01-10",
    title: "Game changer for camping",
    content:
      "The build quality is exceptional. I've used it on three camping trips now and it still looks brand new. The insulation is incredible - ice lasted 3 days!",
    verified: true,
    helpful: 18,
  },
  {
    id: "3",
    author: "Emma Thompson",
    avatar: "/placeholder-user.jpg",
    rating: 4,
    date: "2024-01-08",
    title: "Stylish and functional",
    content:
      "Gets compliments everywhere I take it. The transparent design is so unique and modern. Only wish it came in more color options for the accents.",
    verified: true,
    helpful: 12,
  },
  {
    id: "4",
    author: "David Park",
    avatar: "/placeholder-user.jpg",
    rating: 5,
    date: "2024-01-05",
    title: "Worth every penny",
    content:
      "Initially hesitant about the price, but the quality justifies it completely. This cooler is built to last and performs better than my old expensive brand.",
    verified: true,
    helpful: 31,
  },
]

interface CustomerReviewsProps {
  productId: string
}

export function CustomerReviews({ productId }: CustomerReviewsProps) {
  const [filter, setFilter] = useState("all")
  const [sortBy, setSortBy] = useState("helpful")

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
  const ratingDistribution = [5, 4, 3, 2, 1].map((rating) => ({
    rating,
    count: reviews.filter((review) => review.rating === rating).length,
    percentage: (reviews.filter((review) => review.rating === rating).length / reviews.length) * 100,
  }))

  return (
    <div className="space-y-6">
      {/* Rating Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">{averageRating.toFixed(1)}</div>
              <div className="flex justify-center mb-2">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${i < Math.floor(averageRating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                  />
                ))}
              </div>
              <p className="text-muted-foreground">Based on {reviews.length} reviews</p>
            </div>

            <div className="space-y-2">
              {ratingDistribution.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center gap-2">
                  <span className="text-sm w-8">{rating}★</span>
                  <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-yellow-400 transition-all duration-500"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-muted-foreground w-8">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
        <div className="flex gap-2">
          {["all", "5", "4", "3", "2", "1"].map((rating) => (
            <Button
              key={rating}
              variant={filter === rating ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(rating)}
            >
              {rating === "all" ? "All" : `${rating}★`}
            </Button>
          ))}
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review, index) => (
          <motion.div
            key={review.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar>
                    <AvatarImage src={review.avatar || "/placeholder.svg"} alt={review.author} />
                    <AvatarFallback>
                      {review.author
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold">{review.author}</h4>
                      {review.verified && (
                        <Badge variant="secondary" className="text-xs">
                          Verified Purchase
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mb-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">{review.date}</span>
                    </div>

                    <h5 className="font-medium mb-2">{review.title}</h5>
                    <p className="text-muted-foreground mb-4">{review.content}</p>

                    {review.images && (
                      <div className="flex gap-2 mb-4">
                        {review.images.map((image, i) => (
                          <img
                            key={i}
                            src={image || "/placeholder.svg"}
                            alt="Review"
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        ))}
                      </div>
                    )}

                    <div className="flex items-center gap-4">
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Helpful ({review.helpful})
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
