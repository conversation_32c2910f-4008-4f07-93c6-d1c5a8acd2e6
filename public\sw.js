const CACHE_NAME = "yeye-cooler-v1"
const urlsToCache = [
  "/",
  "/manifest.json",
  "/images/hero/beach-main.jpg",
  "/images/hero/beach-main-pink.png",
  "/images/products/yeye-weekender/main.png",
  "/images/products/yeye-weekender/main-pink.png",
  "/images/products/yeye-classic/main.jpg",
  "/images/products/yeye-classic/main-pink.png",
  "/images/products/yeye-go/main.png",
  "/images/products/yeye-jug/main.jpg",
  "/images/products/yeye-jug/main-pink.jpeg",
  "/images/lifestyle/sunset-dock.jpg",
  "/images/lifestyle/pool-party.jpg",
  "/images/lifestyle/group-picnic.jpg",
  "/images/lifestyle/jug-lifestyle.jpg",
  "/images/technical/diagram.png",
  "/images/technical/color-variants.jpg",
]

self.addEventListener("install", (event) => {
  event.waitUntil(caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache)))
})

self.addEventListener("fetch", (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      if (response) {
        return response
      }
      return fetch(event.request)
    }),
  )
})
