import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { CartProvider } from "@/hooks/use-cart"
import { ColorVariantProvider } from "@/hooks/use-color-variant"
import { CartSidebar } from "@/components/cart-sidebar"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Yeye - Cooler, but clearer",
  description: "The revolutionary transparent cooler collection that puts your style on display.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <ColorVariantProvider>
            <CartProvider>
              {children}
              <CartSidebar />
              <Toaster />
            </CartProvider>
          </ColorVariantProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
