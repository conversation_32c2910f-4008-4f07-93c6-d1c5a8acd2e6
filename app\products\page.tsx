"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { OptimizedImage } from "@/components/ui/optimized-image"
import { Heart, ShoppingCart, Star, ArrowRight } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { useColorVariant } from "@/hooks/use-color-variant"

const products = [
  {
    id: "yeye-go",
    name: "Yeye GO™",
    capacity: "10L",
    price: 199,
    originalPrice: 249,
    rating: 4.8,
    reviews: 324,
    badge: "Perfect for Solo",
    description: "The compact companion for personal adventures. Crystal-clear transparency meets portable perfection.",
    image: "/images/products/yeye-go/main.png",
    href: "/products/yeye-go"
  },
  {
    id: "yeye-classic",
    name: "Yeye CLASSIC™",
    capacity: "18L",
    price: 299,
    originalPrice: 349,
    rating: 4.9,
    reviews: 1247,
    badge: "Most Popular",
    description: "The original all-rounder that started it all. The perfect balance of size, style, and functionality.",
    image: "/images/products/yeye-classic/main.jpg",
    href: "/products/yeye-classic"
  },
  {
    id: "yeye-weekender",
    name: "Yeye WEEKENDER™",
    capacity: "35L",
    price: 449,
    originalPrice: 499,
    rating: 4.7,
    reviews: 892,
    badge: "Family Size",
    description: "The ultimate family companion for extended adventures. Maximum capacity meets premium design.",
    image: "/images/products/yeye-weekender/main.png",
    href: "/products/yeye-weekender"
  },
  {
    id: "yeye-jug",
    name: "Yeye JUG™",
    capacity: "5L",
    price: 149,
    originalPrice: 179,
    rating: 4.6,
    reviews: 567,
    badge: "New",
    description: "The perfect pour companion. Elegant design meets practical functionality for every occasion.",
    image: "/images/products/yeye-jug/main.jpg",
    href: "/products/yeye-jug"
  }
]

export default function ProductsPage() {
  const { addToCart } = useCart()
  const { variant } = useColorVariant()

  const handleAddToCart = (product: typeof products[0]) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1,
    })
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-24 pb-8">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Products</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Header */}
      <div className="container mx-auto px-4 pb-12">
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-5xl font-bold mb-6">Our Products</h1>
          <p className="text-xl text-muted-foreground">
            Discover the complete Yeye™ collection. From personal adventures to family gatherings, 
            we have the perfect transparent cooler for every occasion.
          </p>
        </div>
      </div>

      {/* Products Grid */}
      <div className="container mx-auto px-4 pb-24">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {products.map((product, index) => {
            const savings = product.originalPrice - product.price
            const savingsPercent = Math.round((savings / product.originalPrice) * 100)

            return (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="aspect-square relative overflow-hidden">
                    <OptimizedImage
                      src={product.image}
                      alt={product.name}
                      width={600}
                      height={600}
                      className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                    />
                    {product.badge && (
                      <Badge className="absolute top-4 left-4 z-10">
                        {product.badge}
                      </Badge>
                    )}
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-2xl font-bold mb-2">{product.name}</h3>
                        <p className="text-muted-foreground">{product.description}</p>
                      </div>

                      {/* Rating */}
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < Math.floor(product.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                            />
                          ))}
                        </div>
                        <span className="font-medium">{product.rating}</span>
                        <span className="text-muted-foreground">({product.reviews} reviews)</span>
                      </div>

                      {/* Pricing */}
                      <div className="flex items-center gap-3">
                        <span className="text-3xl font-bold">${product.price}</span>
                        <span className="text-lg text-muted-foreground line-through">${product.originalPrice}</span>
                        <Badge variant="secondary" className="text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30">
                          Save {savingsPercent}%
                        </Badge>
                      </div>

                      {/* Actions */}
                      <div className="space-y-3">
                        <div className="flex gap-3">
                          <Button
                            size="lg"
                            className={`flex-1 ${variant === "pink" ? "bg-pink-500 hover:bg-pink-600 text-white" : ""}`}
                            onClick={() => handleAddToCart(product)}
                          >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Preorder - ${product.price}
                          </Button>
                          <Button variant="outline" size="lg" className="border-border hover:bg-accent bg-transparent">
                            <Heart className="h-4 w-4" />
                          </Button>
                        </div>

                        <Button variant="ghost" asChild className="w-full text-muted-foreground hover:text-foreground">
                          <Link href={product.href}>
                            View Full Details
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
