"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, User, ArrowRight } from "lucide-react"
import Link from "next/link"

const articles = [
  {
    id: "transparent-design-philosophy",
    title: "The Philosophy Behind Transparent Design",
    excerpt:
      "Why we chose transparency as more than just an aesthetic choice - it's about honesty, clarity, and connection with your gear.",
    author: "Yeye Design Team",
    date: "2024-01-20",
    readTime: "5 min read",
    category: "Design",
    image: "/images/products/yeye-classic/main.jpg",
    featured: true,
  },
  {
    id: "perfect-beach-setup",
    title: "The Perfect Beach Day Setup",
    excerpt: "From sunrise to sunset, here's how to maximize your beach experience with the right gear and mindset.",
    author: "<PERSON>",
    date: "2024-01-18",
    readTime: "8 min read",
    category: "Lifestyle",
    image: "/images/hero-beach.jpg",
  },
  {
    id: "ice-retention-science",
    title: "The Science of Ice Retention",
    excerpt: "Deep dive into the physics and engineering that keeps your drinks cold for days, not hours.",
    author: "Dr. <PERSON>",
    date: "2024-01-15",
    readTime: "6 min read",
    category: "Technology",
    image: "/images/technical-diagram.png",
  },
  {
    id: "sustainable-adventures",
    title: "Sustainable Adventures: Leave No Trace",
    excerpt: "How to enjoy the outdoors responsibly while making memories that last a lifetime.",
    author: "Emma Thompson",
    date: "2024-01-12",
    readTime: "7 min read",
    category: "Sustainability",
    image: "/images/sunset-dock.jpg",
  },
  {
    id: "group-picnic-guide",
    title: "The Ultimate Group Picnic Guide",
    excerpt: "Planning the perfect outdoor gathering? Here's everything you need to know for a memorable experience.",
    author: "David Park",
    date: "2024-01-10",
    readTime: "10 min read",
    category: "Lifestyle",
    image: "/images/group-picnic.jpg",
  },
  {
    id: "cooler-care-maintenance",
    title: "Cooler Care: Making It Last a Lifetime",
    excerpt:
      "Simple maintenance tips to keep your transparent cooler looking and performing like new for years to come.",
    author: "Yeye Support Team",
    date: "2024-01-08",
    readTime: "4 min read",
    category: "Care Guide",
    image: "/images/cooler-lime-studio.jpg",
  },
]

const categories = ["All", "Design", "Lifestyle", "Technology", "Sustainability", "Care Guide"]

export default function JournalPage() {
  const featuredArticle = articles.find((article) => article.featured)
  const regularArticles = articles.filter((article) => !article.featured)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="container mx-auto px-4 pt-24 pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-3xl mx-auto"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6">The Yeye Journal</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Stories, insights, and inspiration from the world of transparent living. Discover the philosophy behind our
            products and the adventures they enable.
          </p>
        </motion.div>
      </div>

      {/* Featured Article */}
      {featuredArticle && (
        <div className="container mx-auto px-4 pb-16">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
            <Card className="overflow-hidden">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative aspect-[4/3] lg:aspect-auto">
                  <img
                    src={featuredArticle.image || "/placeholder.svg"}
                    alt={featuredArticle.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge>Featured</Badge>
                  </div>
                </div>
                <CardContent className="p-8 flex flex-col justify-center">
                  <div className="flex items-center gap-4 mb-4">
                    <Badge variant="secondary">{featuredArticle.category}</Badge>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{featuredArticle.date}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>{featuredArticle.readTime}</span>
                    </div>
                  </div>
                  <h2 className="text-3xl font-bold mb-4">{featuredArticle.title}</h2>
                  <p className="text-muted-foreground mb-6">{featuredArticle.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span className="text-sm">{featuredArticle.author}</span>
                    </div>
                    <Button asChild>
                      <Link href={`/journal/${featuredArticle.id}`}>
                        Read Article
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Category Filter */}
      <div className="container mx-auto px-4 pb-8">
        <div className="flex flex-wrap gap-2 justify-center">
          {categories.map((category) => (
            <Button key={category} variant="outline" size="sm">
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Articles Grid */}
      <div className="container mx-auto px-4 pb-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {regularArticles.map((article, index) => (
            <motion.div
              key={article.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="overflow-hidden hover:shadow-lg transition-shadow group">
                <div className="relative aspect-[4/3] overflow-hidden">
                  <img
                    src={article.image || "/placeholder.svg"}
                    alt={article.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary">{article.category}</Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-3 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{article.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{article.readTime}</span>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 line-clamp-3">{article.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-3 w-3" />
                      <span>{article.author}</span>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/journal/${article.id}`}>
                        Read More
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Newsletter Signup */}
      <div className="container mx-auto px-4 pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-2xl mx-auto"
        >
          <Card className="p-8 bg-gradient-to-br from-primary/5 to-blue-500/5">
            <CardContent className="p-0">
              <h2 className="text-2xl font-bold mb-4">Stay in the Loop</h2>
              <p className="text-muted-foreground mb-6">
                Get the latest stories, product updates, and exclusive insights delivered to your inbox.
              </p>
              <div className="flex gap-2 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 rounded-lg border bg-background"
                />
                <Button>Subscribe</Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
