import mockData from "@/data/mockdata.json"
import type { Product, Category, Testimonial, Company } from "@/types"

export function getAllProducts(): Product[] {
  return mockData.products
}

export function getProductById(id: string): Product | null {
  return mockData.products.find((product) => product.id === id) || null
}

export function getProductsByCategory(category: string): Product[] {
  return mockData.products.filter((product) => product.category === category)
}

export function searchProducts(query: string): Product[] {
  const lowercaseQuery = query.toLowerCase()
  return mockData.products.filter(
    (product) =>
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.description.toLowerCase().includes(lowercaseQuery) ||
      product.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)) ||
      product.features.some((feature) => feature.toLowerCase().includes(lowercaseQuery)),
  )
}

export function getFeaturedProducts(limit = 3): Product[] {
  return mockData.products.slice(0, limit)
}

export function getAllCategories(): Category[] {
  return mockData.categories
}

export function getTestimonials(productId?: string): Testimonial[] {
  if (productId) {
    return mockData.testimonials.filter((testimonial) => testimonial.productId === productId)
  }
  return mockData.testimonials
}

export function getCompanyInfo(): Company {
  return mockData.company
}

export function getRelatedProducts(productId: string, limit = 3): Product[] {
  const currentProduct = getProductById(productId)
  if (!currentProduct) return []

  return mockData.products
    .filter((product) => product.id !== productId && product.category === currentProduct.category)
    .slice(0, limit)
}
