import { type NextRequest, NextResponse } from "next/server"
import { getAllProducts, searchProducts, getProductsByCategory } from "@/lib/data"
import type { ApiResponse, Product } from "@/types"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")
    const category = searchParams.get("category")
    const limit = searchParams.get("limit")

    let products = getAllProducts()

    // Apply search filter
    if (query) {
      products = searchProducts(query)
    }

    // Apply category filter
    if (category) {
      products = getProductsByCategory(category)
    }

    // Apply limit
    if (limit) {
      const limitNum = Number.parseInt(limit, 10)
      products = products.slice(0, limitNum)
    }

    const response: ApiResponse<Product[]> = {
      success: true,
      data: products,
      message: `Found ${products.length} products`,
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch products",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
