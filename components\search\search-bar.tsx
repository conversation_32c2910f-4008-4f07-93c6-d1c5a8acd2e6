"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Search, X, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ThumbnailImage } from "@/components/ui/optimized-image"
import { useProducts } from "@/hooks/use-api"
import { cn } from "@/lib/utils"
import Link from "next/link"
import type { Product } from "@/types"

interface SearchBarProps {
  className?: string
  placeholder?: string
  onClose?: () => void
}

export function SearchBar({ className, placeholder = "Search products...", onClose }: SearchBarProps) {
  const [query, setQuery] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const [debouncedQuery, setDebouncedQuery] = useState("")
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, 300)

    return () => clearTimeout(timer)
  }, [query])

  const { data: products, loading } = useProducts(debouncedQuery || undefined)

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setIsOpen(value.length > 0)
  }

  const handleClear = () => {
    setQuery("")
    setIsOpen(false)
    inputRef.current?.focus()
  }

  const handleProductClick = () => {
    setQuery("")
    setIsOpen(false)
    onClose?.()
  }

  const filteredProducts = products?.slice(0, 6) || []

  return (
    <div ref={containerRef} className={cn("relative w-full max-w-md", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          className="pl-10 pr-10 bg-background/80 backdrop-blur-sm border-border/50 focus:border-primary/50 focus:ring-primary/20"
          onFocus={() => query.length > 0 && setIsOpen(true)}
        />
        {query && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
            onClick={handleClear}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute top-full left-0 right-0 mt-2 bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-lg z-50 overflow-hidden"
          >
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : filteredProducts.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                <div className="p-2">
                  <div className="text-xs font-medium text-muted-foreground px-3 py-2">
                    {filteredProducts.length} result{filteredProducts.length !== 1 ? "s" : ""} found
                  </div>
                  {filteredProducts.map((product: Product) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.id}`}
                      onClick={handleProductClick}
                      className="block"
                    >
                      <motion.div
                        whileHover={{ backgroundColor: "hsl(var(--accent))" }}
                        className="flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors"
                      >
                        <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                          <ThumbnailImage
                            src={product.images[0]}
                            alt={product.name}
                            width={48}
                            height={48}
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm text-foreground truncate">{product.name}</h4>
                            <Badge variant="secondary" className="text-xs">
                              {product.badge}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground truncate">{product.shortDescription}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm font-semibold text-foreground">${product.price}</span>
                            <span className="text-xs text-muted-foreground">{product.capacity}</span>
                          </div>
                        </div>
                      </motion.div>
                    </Link>
                  ))}
                </div>
              </div>
            ) : query.length > 0 ? (
              <div className="p-8 text-center">
                <div className="text-muted-foreground mb-2">No products found</div>
                <div className="text-sm text-muted-foreground">Try searching for "cooler", "jug", or "transparent"</div>
              </div>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
