import type { CURRENCIES } from "@/lib/constants"

export type CurrencyCode = keyof typeof CURRENCIES

export function formatPrice(amount: number, currency: CurrencyCode = "USD", locale = "en-US"): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function calculateSavings(
  originalPrice: number,
  currentPrice: number,
): {
  amount: number
  percentage: number
} {
  const amount = originalPrice - currentPrice
  const percentage = Math.round((amount / originalPrice) * 100)
  return { amount, percentage }
}

export function convertCurrency(
  amount: number,
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode,
  exchangeRates: Record<string, number>,
): number {
  if (fromCurrency === toCurrency) return amount

  // Convert to USD first, then to target currency
  const usdAmount = fromCurrency === "USD" ? amount : amount / exchangeRates[fromCurrency]
  return toCurrency === "USD" ? usdAmount : usdAmount * exchangeRates[toCurrency]
}
