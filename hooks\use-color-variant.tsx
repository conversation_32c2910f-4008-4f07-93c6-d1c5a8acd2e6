"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

type ColorVariant = "clear" | "pink"

interface ColorVariantContextType {
  variant: ColorVariant
  setVariant: (variant: ColorVariant) => void
  toggleVariant: () => void
}

const ColorVariantContext = createContext<ColorVariantContextType | undefined>(undefined)

export function ColorVariantProvider({ children }: { children: ReactNode }) {
  const [variant, setVariant] = useState<ColorVariant>("clear")

  // Load variant from localStorage on mount
  useEffect(() => {
    const savedVariant = localStorage.getItem("yeye-color-variant") as ColorVariant
    if (savedVariant && (savedVariant === "clear" || savedVariant === "pink")) {
      setVariant(savedVariant)
    }
  }, [])

  // Save variant to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("yeye-color-variant", variant)
  }, [variant])

  const toggleVariant = () => {
    setVariant((prev) => (prev === "clear" ? "pink" : "clear"))
  }

  return (
    <ColorVariantContext.Provider value={{ variant, setVariant, toggleVariant }}>
      {children}
    </ColorVariantContext.Provider>
  )
}

export function useColorVariant() {
  const context = useContext(ColorVariantContext)
  if (context === undefined) {
    throw new Error("useColorVariant must be used within a ColorVariantProvider")
  }
  return context
}
