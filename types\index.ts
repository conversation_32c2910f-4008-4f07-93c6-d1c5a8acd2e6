export interface Product {
  id: string
  name: string
  capacity: string
  price: number
  originalPrice: number
  rating: number
  reviews: number
  badge: string
  category: string
  preorder: boolean
  description: string
  shortDescription: string
  features: string[]
  specifications: Record<string, string>
  images: string[]
  lifestyle: string
  tags: string[]
}

export interface Category {
  id: string
  name: string
  description: string
  image: string
}

export interface Testimonial {
  id: string
  name: string
  role: string
  avatar: string
  rating: number
  content: string
  productId?: string
}

export interface Company {
  name: string
  tagline: string
  description: string
  founded: string
  mission: string
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface CartItem {
  id: string
  name: string
  price: number
  image: string
  quantity: number
}

export interface SearchFilters {
  query?: string
  category?: string
  priceRange?: [number, number]
  rating?: number
  tags?: string[]
}
