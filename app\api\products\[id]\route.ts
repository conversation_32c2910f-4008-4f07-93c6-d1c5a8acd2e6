import { NextResponse } from "next/server"
import { getProductById, getRelatedProducts } from "@/lib/data"
import type { ApiResponse, Product } from "@/types"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const product = getProductById(params.id)

    if (!product) {
      const response: ApiResponse<never> = {
        success: false,
        error: "Product not found",
      }
      return NextResponse.json(response, { status: 404 })
    }

    const relatedProducts = getRelatedProducts(params.id)

    const response: ApiResponse<Product & { relatedProducts: Product[] }> = {
      success: true,
      data: {
        ...product,
        relatedProducts,
      },
      message: "Product found",
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch product",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
