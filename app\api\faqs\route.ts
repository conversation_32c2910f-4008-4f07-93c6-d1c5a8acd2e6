import { type NextRequest, NextResponse } from "next/server"
import { getAllFAQs, getFAQsByCategory } from "@/lib/data"
import type { ApiResponse, FAQ } from "@/types"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")

    let faqs = getAllFAQs()

    if (category) {
      faqs = getFAQsByCategory(category)
    }

    const response: ApiResponse<FAQ[]> = {
      success: true,
      data: faqs,
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch FAQs",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
