"use client"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { fallbackImages } from "@/lib/image-paths"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  fallback?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  fallback = fallbackImages.default,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoading(false)
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    console.warn(`Failed to load image: ${src}`)
  }

  if (hasError) {
    return (
      <div className={cn("bg-muted flex items-center justify-center rounded-lg", className)}>
        <img
          src={fallback}
          alt={`${alt} (fallback)`}
          width={width}
          height={height}
          className="object-cover w-full h-full opacity-50"
          onError={(e) => {
            // If fallback also fails, show a simple placeholder
            const target = e.target as HTMLImageElement
            target.style.display = 'none'
            target.parentElement!.innerHTML = `
              <div class="flex items-center justify-center w-full h-full text-muted-foreground text-sm">
                <div class="text-center">
                  <div class="mb-2">📷</div>
                  <div>Image not found</div>
                </div>
              </div>
            `
          }}
        />
      </div>
    )
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-r from-muted via-muted/50 to-muted animate-pulse rounded-lg flex items-center justify-center">
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      )}

      <img
        src={src || fallback}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          "object-cover w-full h-full",
          className,
        )}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  )
}

// Preset configurations for common use cases
export function ProductImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      fallback={fallbackImages.product}
      {...props}
    />
  )
}

export function HeroImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      fallback={fallbackImages.hero}
      {...props}
    />
  )
}

export function ThumbnailImage({ src, alt, className, width, height, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      fallback={fallbackImages.thumbnail}
      {...props}
    />
  )
}
