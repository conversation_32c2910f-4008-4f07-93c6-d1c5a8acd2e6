import type { Metadata } from "next"
import { SITE_CONFIG } from "@/lib/constants"
import type { Product } from "@/types/product"

interface SEOProps {
  title?: string
  description?: string
  image?: string
  url?: string
  type?: "website" | "article" | "product"
}

export function generateSEO({
  title,
  description = SITE_CONFIG.description,
  image = SITE_CONFIG.ogImage,
  url = SITE_CONFIG.url,
  type = "website",
}: SEOProps = {}): Metadata {
  const seoTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.name

  return {
    title: seoTitle,
    description,
    openGraph: {
      title: seoTitle,
      description,
      url,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: seoTitle,
        },
      ],
      type,
    },
    twitter: {
      card: "summary_large_image",
      title: seoTitle,
      description,
      images: [image],
      creator: "@yeye",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  }
}

export function generateProductSEO(product: Product): Metadata {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    image: product.images,
    brand: {
      "@type": "Brand",
      name: "Yeye",
    },
    offers: {
      "@type": "Offer",
      price: product.price,
      priceCurrency: "USD",
      availability: product.inStock ? "https://schema.org/InStock" : "https://schema.org/PreOrder",
      seller: {
        "@type": "Organization",
        name: "Yeye",
      },
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: product.rating,
      reviewCount: product.reviews,
    },
  }

  return {
    ...generateSEO({
      title: product.name,
      description: product.description,
      image: product.images[0],
      type: "product",
    }),
    other: {
      "application/ld+json": JSON.stringify(structuredData),
    },
  }
}
