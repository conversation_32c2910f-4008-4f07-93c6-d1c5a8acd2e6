"use client"

import { useState, useEffect } from "react"
import type { ApiResponse } from "@/types"

export function useApi<T>(url: string, options?: RequestInit) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(url, options)
      const result: ApiResponse<T> = await response.json()

      if (result.success && result.data) {
        setData(result.data)
      } else {
        setError(result.error || "Unknown error occurred")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Network error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [url])

  return { data, loading, error, refetch: () => fetchData() }
}

export function useProducts(query?: string, category?: string) {
  const params = new URLSearchParams()
  if (query) params.append("q", query)
  if (category) params.append("category", category)

  const url = `/api/products${params.toString() ? `?${params.toString()}` : ""}`
  return useApi<any[]>(url)
}

export function useProduct(id: string) {
  return useApi<any>(`/api/products/${id}`)
}
