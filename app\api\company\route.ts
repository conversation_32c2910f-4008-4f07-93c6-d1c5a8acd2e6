import { NextResponse } from "next/server"
import { getCompanyInfo } from "@/lib/data"
import type { ApiResponse, Company } from "@/types"

export async function GET() {
  try {
    const company = getCompanyInfo()

    const response: ApiResponse<Company> = {
      success: true,
      data: company,
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch company information",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
