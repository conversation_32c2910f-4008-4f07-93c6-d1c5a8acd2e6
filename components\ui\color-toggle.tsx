"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useColorVariant } from "@/hooks/use-color-variant"
import { Palette } from "lucide-react"

export function ColorToggle() {
  const { variant, toggleVariant } = useColorVariant()

  return (
    <div className="flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border rounded-full p-1 shadow-lg">
      <Button
        variant={variant === "clear" ? "default" : "ghost"}
        size="sm"
        onClick={() => toggleVariant()}
        className="rounded-full h-8 px-3"
      >
        <div className="w-3 h-3 rounded-full bg-gray-200 border border-gray-300 mr-2" />
        Clear
      </Button>
      <Button
        variant={variant === "pink" ? "default" : "ghost"}
        size="sm"
        onClick={() => toggleVariant()}
        className="rounded-full h-8 px-3"
      >
        <div className="w-3 h-3 rounded-full bg-pink-500 mr-2" />
        Pink
      </Button>
      {variant === "pink" && (
        <Badge className="ml-1 bg-pink-100 text-pink-800 border-pink-200">
          <Palette className="w-3 h-3 mr-1" />
          New
        </Badge>
      )}
    </div>
  )
}
