import { type NextRequest, NextResponse } from "next/server"
import { getAllTestimonials, getTestimonialsByProduct } from "@/lib/data"
import type { ApiResponse, Testimonial } from "@/types"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get("product")

    let testimonials = getAllTestimonials()

    if (productId) {
      testimonials = getTestimonialsByProduct(productId)
    }

    const response: ApiResponse<Testimonial[]> = {
      success: true,
      data: testimonials,
    }

    return NextResponse.json(response)
  } catch (error) {
    const response: ApiResponse<never> = {
      success: false,
      error: "Failed to fetch testimonials",
    }

    return NextResponse.json(response, { status: 500 })
  }
}
